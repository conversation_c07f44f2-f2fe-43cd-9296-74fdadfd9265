package com.multiplier.integration.service

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.google.protobuf.Struct
import com.google.protobuf.Value
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.contract.onboarding.schema.BulkOnboardDataSpec
import com.multiplier.fieldmapping.grpc.schema.Profile
import com.multiplier.grpc.common.contract.v2.Contract
import com.multiplier.grpc.common.country.v2.Country
import com.multiplier.integration.adapter.api.ContractOnboardingServiceAdapter
import com.multiplier.integration.service.fieldmappings.GroupedEmployeeData
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.NewCompanyServiceAdapter
import com.multiplier.integration.adapter.api.FieldMappingServiceAdapter
import com.multiplier.integration.adapter.api.resources.knit.ErrorResponse
import com.multiplier.integration.adapter.api.resources.knit.Field
import com.multiplier.integration.adapter.api.resources.knit.FieldData
import com.multiplier.integration.adapter.api.resources.knit.FieldValues
import com.multiplier.integration.adapter.api.resources.knit.GetFieldValuesResponse
import com.multiplier.integration.adapter.model.BulkContractOnboardingRequest
import com.multiplier.integration.adapter.model.OnboardingType
import com.multiplier.integration.repository.CompanyIntegrationRepository
import com.multiplier.integration.repository.ExternalPlatformValuesRepository
import com.multiplier.integration.repository.LegalEntityMappingRepository
import com.multiplier.integration.repository.ReceivedEventRepository
import com.multiplier.integration.repository.ReceivedEventsArchiveRepository

import com.multiplier.integration.repository.model.JpaCompanyIntegration
import com.multiplier.integration.repository.model.JpaExternalPlatformValues
import com.multiplier.integration.repository.model.JpaLegalEntityMapping
import com.multiplier.integration.repository.model.JpaReceivedEvent
import com.multiplier.integration.repository.model.JpaReceivedEventArchive
import com.multiplier.integration.repository.model.LegalMappingStatus
import com.multiplier.integration.service.exception.EntityNotFoundException
import com.multiplier.integration.sync.DataMapper
import com.multiplier.integration.sync.model.MaritalStatus
import com.multiplier.integration.types.Company
import com.multiplier.integration.types.FieldMappingV2
import com.multiplier.integration.types.Gender
import com.multiplier.integration.types.IntegrationEntityMappingStatusOutput
import com.multiplier.integration.types.IntegrationFieldsMappingContractorOutput
import com.multiplier.integration.types.IntegrationFieldsMappingOutputV2
import com.multiplier.integration.types.TaskResponse
import com.multiplier.integration.types.UnmappedField
import com.multiplier.integration.utils.formatExternalKey
import com.multiplier.integration.utils.mapPlatformIdToKnitAppId
import com.multiplier.integration.utils.populateKeyFromLabel
import com.multiplier.integration.utils.populateLabelFromKey
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Duration
import java.time.LocalDateTime
import java.util.*

@Service
class FieldMappingService(
    private val companyIntegrationRepository: CompanyIntegrationRepository,
    private val knitAdapter: KnitAdapter,
    private val receivedEventRepository: ReceivedEventRepository,
    private val newCompanyServiceAdapter: NewCompanyServiceAdapter,
    private val contractOnboardingServiceAdapter: ContractOnboardingServiceAdapter,
    private val legalEntityMappingRepository: LegalEntityMappingRepository,
    private val externalPlatformValuesRepository: ExternalPlatformValuesRepository,
    private val receivedEventsArchiveRepository: ReceivedEventsArchiveRepository,
    private val fieldMappingServiceAdapter: FieldMappingServiceAdapter,
) {

    private val log = KotlinLogging.logger {}
    private val objectMapper = jacksonObjectMapper()
    private val dataMapper = DataMapper()

    fun formatUnmappedFields(
        updatedExternalPlatformValues: List<JpaExternalPlatformValues>,
        thirdPartyFields: List<UnmappedField>,
        integration: JpaCompanyIntegration,
    ): List<UnmappedField> {
        if (thirdPartyFields.isEmpty()) return emptyList()

        val externalKeyToValuesMap = buildExternalKeyToValuesMap(updatedExternalPlatformValues)
        val unmappedFields = thirdPartyFields.map { field ->
            createUnmappedField(field, externalKeyToValuesMap, integration)
        }
        return handleExternalFieldWithSubFields(unmappedFields)
    }
    fun getKnitFields(
        integrationId: Long,
        companyId: Long,
        integration: JpaCompanyIntegration,
    ): List<FieldData> {
        log.info("Fetching data specs from KNIT for integrationId $integrationId")

        return try {
            val platformKeys = runBlocking {
                knitAdapter.getAllFields(
                    companyId,
                    integration.platform.id!!,
                    mapPlatformIdToKnitAppId(integration.platform.name)
                )
            }

            buildExternalFieldsList(platformKeys.data)
        } catch (e: Exception) {
            log.error("Error fetching KNIT fields for integrationId $integrationId", e)
            emptyList()
        }
    }

    private fun getOnboardDataSpecs(
        matchedLegalEntity: CompanyOuterClass.LegalEntity?,
        companyId: Long,
    ): List<BulkOnboardDataSpec> {
        if (matchedLegalEntity == null) {
            log.info("Fetching data specs for contractor field mapping")
            val getOnboardDataSpecsRequest = BulkContractOnboardingRequest(
                companyId = companyId,
                entityId = 0,
                context = OnboardingType.FREELANCER,
                countryCode = Country.CountryCode.COUNTRY_CODE_USA,
                contractType = Contract.ContractType.CONTRACT_TYPE_FREELANCER,
                data = GroupedEmployeeData(employeeData = emptyMap())
            )
            return contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(getOnboardDataSpecsRequest)
        }
        log.info("Fetching data specs from onboarding service for legal entity ${matchedLegalEntity.legalName}")
        val getOnboardDataSpecsRequest = BulkContractOnboardingRequest(
            companyId = companyId,
            entityId = matchedLegalEntity.id!!,
            context = OnboardingType.GLOBAL_PAYROLL,
            countryCode = Country.CountryCode.valueOf("COUNTRY_CODE_${matchedLegalEntity.address.country}"),
            contractType = Contract.ContractType.CONTRACT_TYPE_HR_MEMBER,
            data = GroupedEmployeeData(employeeData = emptyMap())
        )
        return contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(getOnboardDataSpecsRequest)
    }

    private fun handleExternalFieldWithSubFields(externalFields: List<UnmappedField>): List<UnmappedField> {
        val typeField = externalFields.find { it.key == "${COMPENSATION_VARIABLE_PREFIX}.type" }
        val planIdField = externalFields.find { it.key == "${COMPENSATION_VARIABLE_PREFIX}.planId" }

        return externalFields.map { field ->
            if (field.key in COMPENSATION_SUB_FIELDS) {
                field.apply {
                    subFields = listOfNotNull(typeField, planIdField)
                }
            } else {
                field
            }
        }
    }

    fun formatKnitFields(externalFields: List<FieldData>?): List<UnmappedField> {
        return externalFields?.map { field ->
            val isMapped = field.mappedKey != null
            val formatWithCustomFieldKey = formatExternalKey(field, isMapped)

            dataMapper.map(
                formatWithCustomFieldKey,
                field.label,
                isMapped,
                field.fieldId,
                field.dataType,
                field.fieldFromApp,
                field.isCustomField ?: false
            ).build()
        } ?: emptyList()
    }

    fun getIntegrationLegalEntityMappings(integrationId: Long): List<IntegrationEntityMappingStatusOutput> {
        val integration = findIntegrationById(integrationId)
        val existingEntityMappings = legalEntityMappingRepository.findByIntegrationId(integrationId)
        val existingEntityIds = existingEntityMappings.map { it.entityId }.toSet()

        log.info("Fetching legal entities for company ${integration.companyId}")
        val legalEntities = newCompanyServiceAdapter.getLegalEntities(integration.companyId)
        val entityIdToLegalEntity = legalEntities.associateBy { it.id }

        // Create new entity mappings for entities that don't exist yet
        val newEntityMappings = createNewEntityMappings(
            legalEntities, existingEntityIds, integration, integrationId
        )

        // Save new mappings if any
        val allEntityMappings = if (newEntityMappings.isNotEmpty()) {
            legalEntityMappingRepository.saveAll(newEntityMappings)
            legalEntityMappingRepository.findByIntegrationId(integrationId)
        } else {
            existingEntityMappings
        }

        return buildEntityMappingOutputs(allEntityMappings, entityIdToLegalEntity, integrationId)
    }

    @Transactional
    fun saveIntegrationEntityMappingStatus(entityMappingId: Long, enableDataSync: Boolean = true): TaskResponse {
        val legalEntityMapping = findLegalEntityMappingById(entityMappingId)
        legalEntityMapping.isEnabled = enableDataSync

        if (!enableDataSync) {
            legalEntityMappingRepository.save(legalEntityMapping)
            return createSuccessResponse("Successfully updated status")
        }

        if (legalEntityMapping.status != LegalMappingStatus.FULLY_MAPPED) {
            return createErrorResponse("Need to map all fields to enable sync for this legal entity")
        }

        legalEntityMappingRepository.save(legalEntityMapping)
        updateReceivedEventsForEnabledEntity(legalEntityMapping)

        return createSuccessResponse("Successfully updated status")
    }

    private fun findLegalEntityMappingById(entityMappingId: Long): JpaLegalEntityMapping {
        return legalEntityMappingRepository.findById(entityMappingId).orElseThrow {
            EntityNotFoundException("Not found legal entity mapping with id = $entityMappingId")
        }
    }

    private fun createSuccessResponse(message: String): TaskResponse {
        return TaskResponse.newBuilder()
            .success(true)
            .message(message)
            .build()
    }

    private fun createErrorResponse(message: String): TaskResponse {
        return TaskResponse.newBuilder()
            .success(false)
            .message(message)
            .build()
    }

    private fun updateReceivedEventsForEnabledEntity(legalEntityMapping: JpaLegalEntityMapping) {
        val integration = findIntegrationById(legalEntityMapping.integrationId)

        if (!integration.enabled || !integration.incomingSyncEnabled) {
            return
        }

        val receivedEvents = receivedEventRepository.findByIntegrationIdAndEntityCountryAndIsEntityEnabledAndProcessed(
            integration.accountToken,
            legalEntityMapping.entityCountry,
            isEntityEnabled = false,
            processed = true
        )

        val archivedReceivedEvents = receivedEventsArchiveRepository.findByIntegrationIdAndEntityCountryAndIsEntityEnabledAndProcessed(
            integration.accountToken,
            legalEntityMapping.entityCountry,
            isEntityEnabled = false,
            processed = true
        )

        val updatedReceivedEvents = buildList {
            addAll(receivedEvents.map { event ->
                event.apply {
                    isEntityEnabled = true
                    processed = false
                }
            })

            if (archivedReceivedEvents.isNotEmpty()) {
                addAll(restoreArchivedReceivedEventsForEntityRetry(archivedReceivedEvents))
                receivedEventsArchiveRepository.deleteAll(archivedReceivedEvents)
            }
        }

        if (updatedReceivedEvents.isNotEmpty()) {
            receivedEventRepository.saveAll(updatedReceivedEvents)
        }
    }

    private fun getDepartmentFieldValues(
        integration: JpaCompanyIntegration,
        fieldId: String
    ): GetFieldValuesResponse {
        log.info("Department field $fieldId returned empty values, trying departments.list endpoint")

        return try {
            val deptResp = knitAdapter.getDepartmentsList(
                integration.companyId,
                integration.platform.id!!,
                mapPlatformIdToKnitAppId(integration.platform.name)
            )

            if (deptResp.success && !deptResp.data?.departments.isNullOrEmpty()) {
                createSuccessfulDepartmentResponse(deptResp)
            } else {
                createFailedDepartmentResponse(deptResp.responseCode)
            }
        } catch (e: Exception) {
            log.error("Error calling departments list endpoint", e)
            createErrorDepartmentResponse(e)
        }
    }

    private fun createSuccessfulDepartmentResponse(
        deptResp: DepartmentsListResponse
    ): GetFieldValuesResponse {
        val fieldValues = deptResp.data?.departments?.map { dept ->
            FieldValues(id = dept.id, label = dept.name)
        } ?: emptyList()

        return GetFieldValuesResponse(
            success = true,
            data = Field(fields = fieldValues),
            responseCode = deptResp.responseCode
        )
    }

    private fun createFailedDepartmentResponse(responseCode: Int?): GetFieldValuesResponse {
        log.warn("Departments list endpoint returned empty response")
        return GetFieldValuesResponse(
            success = false,
            error = ErrorResponse(msg = "No departments found or error occurred"),
            responseCode = responseCode
        )
    }

    private fun createErrorDepartmentResponse(e: Exception): GetFieldValuesResponse {
        return GetFieldValuesResponse(
            success = false,
            error = ErrorResponse(msg = e.message ?: "Unknown error"),
            responseCode = null
        )
    }

    // Helper methods for optimized code
    private fun buildExternalKeyToValuesMap(
        externalPlatformValues: List<JpaExternalPlatformValues>
    ): Map<String, List<String>?> {
        return externalPlatformValues.associate { it.fieldId to it.values }
    }

    private fun createUnmappedField(
        field: UnmappedField,
        externalKeyToValuesMap: Map<String, List<String>?>,
        integration: JpaCompanyIntegration
    ): UnmappedField {
        val updatedKey = if (!field.isMappedByThirdParty) {
            field.key.populateKeyFromLabel(field.label)
        } else {
            field.key
        }

        val childrenMapping = createChildrenMapping(field, externalKeyToValuesMap, integration)

        return dataMapper.map(
            key = updatedKey,
            label = field.label.populateLabelFromKey(field.key?.replace(CUSTOM_FIELDS_PREFIX, "")),
            isMappedByThirdParty = field.isMappedByThirdParty,
            fieldId = field.fieldId,
            type = field.type,
            fieldFromApp = field.fieldFromApp,
            isCustomField = field.isCustomField
        ).apply {
            childrenMapping?.let { children(it) }
        }.build()
    }

    private fun createChildrenMapping(
        field: UnmappedField,
        externalKeyToValuesMap: Map<String, List<String>?>,
        integration: JpaCompanyIntegration
    ): List<UnmappedField>? {
        return externalKeyToValuesMap[field.fieldId]?.map { child ->
            val mappedValue = objectMapper.readValue(child, FieldValues::class.java)
            val childKey = if (field.isCustomField && integration.platform.isSpecialEnum) {
                mappedValue.id
            } else {
                mappedValue.label
            }

            dataMapper.map(
                key = childKey,
                label = mappedValue.label,
                isMappedByThirdParty = field.isMappedByThirdParty,
                fieldId = null,
                type = "STRING",
                fieldFromApp = null,
                isCustomField = field.isCustomField
            ).build()
        }
    }

    private fun buildExternalFieldsList(data: com.multiplier.integration.adapter.api.resources.knit.FieldDataList?): List<FieldData> {
        return buildList {
            data?.run {
                // Add default fields
                default?.let { addAll(it) }

                // Add mapped and unmapped custom fields
                listOfNotNull(mapped, unmapped)
                    .flatten()
                    .onEach { it.isCustomField = true }
                    .let { addAll(it) }
            }
        }
    }

    private fun findIntegrationById(integrationId: Long): JpaCompanyIntegration {
        return companyIntegrationRepository.findById(integrationId)
            .orElseThrow { EntityNotFoundException("Not found company integration with integrationId=$integrationId") }
    }

    private fun createNewEntityMappings(
        legalEntities: List<CompanyOuterClass.LegalEntity>,
        existingEntityIds: Set<Long>,
        integration: JpaCompanyIntegration,
        integrationId: Long
    ): List<JpaLegalEntityMapping> {
        return legalEntities
            .filter { it.id !in existingEntityIds }
            .onEach { log.info("Found new legal entity to be added: ${it.legalName}") }
            .map { legalEntity ->
                JpaLegalEntityMapping(
                    entityId = legalEntity.id,
                    entityName = legalEntity.legalName,
                    companyId = integration.companyId,
                    status = LegalMappingStatus.UNMAPPED,
                    isEnabled = false,
                    integrationId = integrationId,
                    entityCountry = legalEntity.address.country
                )
            }
    }

    private fun buildEntityMappingOutputs(
        entityMappings: List<JpaLegalEntityMapping>,
        entityIdToLegalEntity: Map<Long, CompanyOuterClass.LegalEntity>,
        integrationId: Long
    ): List<IntegrationEntityMappingStatusOutput> {
        return entityMappings.map { mapping ->
            val legalEntity = entityIdToLegalEntity[mapping.entityId]
            val legalEntityMapped = dataMapper.map(legalEntity)

            IntegrationEntityMappingStatusOutput.newBuilder()
                .entityMappingId(mapping.id)
                .integrationId(integrationId)
                .isEnabled(mapping.isEnabled)
                .legalEntity(legalEntityMapped)
                .entityMappingStatus(com.multiplier.integration.types.LegalMappingStatus.valueOf(mapping.status.name))
                .company(
                    Company.newBuilder()
                        .id(mapping.companyId)
                        .build()
                )
                .build()
        }
    }

    fun getExternalEnumValues(
        integration: JpaCompanyIntegration,
        externalFields: List<FieldData>?,
    ): List<JpaExternalPlatformValues>? {
        val cachedExternalValues = externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integration.id!!)
        val cacheEnumMapping = cachedExternalValues.associateBy { it.fieldId }

        val enumFieldsToFetch = getEnumFieldsToFetch(externalFields, cacheEnumMapping)
        if (enumFieldsToFetch.isEmpty()) {
            return cachedExternalValues
        }

        val fetchedEnumValues = fetchEnumValuesAsync(integration, enumFieldsToFetch)
        if (fetchedEnumValues.isEmpty()) {
            return cachedExternalValues
        }

        val updatedPlatformValues = createUpdatedPlatformValues(fetchedEnumValues, cacheEnumMapping, integration.id!!)
        externalPlatformValuesRepository.saveAll(updatedPlatformValues)

        return externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integration.id!!)
    }

    private fun getEnumFieldsToFetch(
        externalFields: List<FieldData>?,
        cacheEnumMapping: Map<String, JpaExternalPlatformValues>
    ): List<Pair<String, String?>> {
        val enumExternalFields = externalFields?.filter { field ->
            field.fieldId != null && (field.dataType == "ENUM" || field.fieldId.contains(DEPARTMENT_FIELD_IDENTIFIER))
        } ?: return emptyList()

        return enumExternalFields.filter { field ->
            val fieldId = field.fieldId!!
            !cacheEnumMapping.containsKey(fieldId) || isCacheExpired(cacheEnumMapping[fieldId])
        }.map { field ->
            Pair(field.fieldId!!, field.mappedKey)
        }
    }

    private fun isCacheExpired(cachedValue: JpaExternalPlatformValues?): Boolean {
        return cachedValue?.let { cached ->
            Duration.between(cached.updatedOn, LocalDateTime.now()).toDays() >= CACHE_EXPIRY_DAYS
        } ?: false
    }

    private fun fetchEnumValuesAsync(
        integration: JpaCompanyIntegration,
        enumFieldsToFetch: List<Pair<String, String?>>
    ): Map<String, Pair<GetFieldValuesResponse, String?>> {
        return runBlocking {
            enumFieldsToFetch.map { (fieldId, mappedKey) ->
                async {
                    fetchSingleEnumValue(integration, fieldId, mappedKey)
                }
            }.awaitAll()
                .filterNotNull()
                .toMap()
        }
    }

    private suspend fun fetchSingleEnumValue(
        integration: JpaCompanyIntegration,
        fieldId: String,
        mappedKey: String?
    ): Pair<String, Pair<GetFieldValuesResponse, String?>>? {
        var resp = knitAdapter.getFieldValues(
            integration.companyId,
            integration.platform.id!!,
            mapPlatformIdToKnitAppId(integration.platform.name),
            fieldId
        )

        // Handle department fields specially
        if (fieldId.contains(DEPARTMENT_FIELD_IDENTIFIER) && resp.data?.fields.isNullOrEmpty()) {
            val deptResp = getDepartmentFieldValues(integration, fieldId)
            if (deptResp.success && !deptResp.data?.fields.isNullOrEmpty()) {
                resp = deptResp
            } else {
                log.info("Falling back to original field values response for department field $fieldId")
            }
        }

        return if (resp.success) fieldId to (resp to mappedKey) else null
    }

    private fun createUpdatedPlatformValues(
        fetchedEnumValues: Map<String, Pair<GetFieldValuesResponse, String?>>,
        cacheEnumMapping: Map<String, JpaExternalPlatformValues>,
        integrationId: Long
    ): List<JpaExternalPlatformValues> {
        return fetchedEnumValues.mapNotNull { (fieldId, result) ->
            val updatedValues = result.first.data?.fields?.map { objectMapper.writeValueAsString(it) }

            cacheEnumMapping[fieldId]?.apply {
                values = updatedValues
                updatedOn = LocalDateTime.now()
            } ?: JpaExternalPlatformValues(
                fieldId = fieldId,
                integrationId = integrationId,
                mappedKey = result.second,
                values = updatedValues
            )
        }
    }

    fun getFixedEnumValues(
        externalPlatformValues: List<JpaExternalPlatformValues>?,
        integrationId: Long,
        thirdPartyFields: List<UnmappedField>,
    ): List<JpaExternalPlatformValues> {
        val dynamicEnumKeys = externalPlatformValues?.mapTo(mutableSetOf()) { it.mappedKey } ?: emptySet()
        val mappedKeyToFieldId = thirdPartyFields
            .filter { !it.key.isNullOrBlank() }
            .associate { it.key to it.fieldId }

        val missingFixedEnumValues = FIXED_ENUM_MAPPINGS
            .filterKeys { it !in dynamicEnumKeys }
            .map { (key, enumValues) ->
                createFixedEnumPlatformValue(key, enumValues, integrationId, mappedKeyToFieldId)
            }

        return (externalPlatformValues ?: emptyList()) + missingFixedEnumValues
    }

    private fun createFixedEnumPlatformValue(
        key: String,
        enumValues: Array<out Enum<*>>,
        integrationId: Long,
        mappedKeyToFieldId: Map<String?, String?>
    ): JpaExternalPlatformValues {
        val enumValueStrings = enumValues.map { enumValue ->
            objectMapper.writeValueAsString(
                mapOf(
                    "id" to enumValue.name,
                    "label" to enumValue.name
                )
            )
        }

        return JpaExternalPlatformValues(
            mappedKey = key,
            integrationId = integrationId,
            fieldId = mappedKeyToFieldId[key] ?: key,
            values = enumValueStrings
        )
    }
    @Transactional
    fun handleFieldMappingsOnDisconnection(integrationId: Long) {
        log.info("Unmapped entity mappings and deleting field mappings by integrationId: $integrationId")

        try {
            updateAndSaveEntityMappingsOnDisconnection(integrationId)
            updateAndSaveEnumValuesOnDisconnection(integrationId)
        } catch (e: Exception) {
            log.error("[HandleFieldMappingsOnDisconnection] Throw exception: ${e.message} ", e)
        }
    }

    private fun updateAndSaveEntityMappingsOnDisconnection(integrationId: Long) {
        val entityMappings = legalEntityMappingRepository.findByIntegrationId(integrationId)
        val updatedEntityMappings = entityMappings.map {
            it.apply {
                isEnabled = false
                status = LegalMappingStatus.UNMAPPED
            }
        }

        if (updatedEntityMappings.isNotEmpty()) {
            legalEntityMappingRepository.saveAll(updatedEntityMappings)
        }
    }

    private fun updateAndSaveEnumValuesOnDisconnection(integrationId: Long) {
        val externalPlatformValues = externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId)
        val updatedExternalPlatformValues = externalPlatformValues.map {
            it.apply {
                isDeleted = true
            }
        }

        if (updatedExternalPlatformValues.isNotEmpty()) {
            externalPlatformValuesRepository.saveAll(updatedExternalPlatformValues)
        }
    }

    private fun restoreArchivedReceivedEventsForEntityRetry(receivedEvents: List<JpaReceivedEventArchive>): List<JpaReceivedEvent> {
        return receivedEvents.map { event ->
            JpaReceivedEvent(
                eventId = event.eventId,
                syncId = event.syncId,
                integrationId = event.integrationId,
                eventType = event.eventType,
                syncDataType = event.syncDataType,
                errors = event.errors,
                identifiervalue = event.identifiervalue,
                receivedTime = event.receivedTime,
                data = event.data,
                confirmedByUser = event.confirmedByUser,
                processed = false,
                isEntityEnabled = true,
                entityId = event.entityId,
                entityCountry = event.entityCountry
            )
        }
    }

    fun getKnitFieldsV2(jpaCompanyIntegration: JpaCompanyIntegration): List<FieldMappingV2> {
        val knitFields = getKnitFields(
            jpaCompanyIntegration.id!!,
            jpaCompanyIntegration.companyId,
            jpaCompanyIntegration
        )
        val externalPlatformValues = getExternalEnumValues(jpaCompanyIntegration, knitFields)
        val thirdPartyFields = formatKnitFields(knitFields)
        val updatedExternalPlatformValues =
            getFixedEnumValues(externalPlatformValues, jpaCompanyIntegration.id!!, thirdPartyFields)
        val unmappedFieldsOutput = formatUnmappedFields(
            updatedExternalPlatformValues,
            thirdPartyFields,
            jpaCompanyIntegration
        )
        
        val baseFields = unmappedFieldsOutput.map { FieldMappingV2(
            it.key,
            it.label,
            false,
            if (it.children != null) it.children.map { child ->
                FieldMappingV2(
                    child.key,
                    child.label,
                    false,
                    emptyList()
                )
            } else emptyList<FieldMappingV2>()
        ) }
        
        // Add Hibob bank account fields if platform is Hibob
        return if (jpaCompanyIntegration.platform.name.equals("Hibob", ignoreCase = true)) {
            baseFields + getHibobBankAccountFields()
        } else {
            baseFields
        }
    }

    private fun getHibobBankAccountFields(): List<FieldMappingV2> {
        return listOf(
            FieldMappingV2("bankAccountsTable.bankAccountType", "Bank Table - Bank Account Type", false, listOf(
                FieldMappingV2("Checking", "Checking", false, emptyList()),
                FieldMappingV2("Savings", "Savings", false, emptyList())
            )),
            FieldMappingV2("bankAccountsTable.routingNumber", "Bank Table - Routing Number", false, emptyList()),
            FieldMappingV2("bankAccountsTable.accountNickname", "Bank Table - Account Nickname", false, emptyList()),
            FieldMappingV2("bankAccountsTable.accountNumber", "Bank Table - Account Number", false, emptyList()),
            FieldMappingV2("bankAccountsTable.bankName", "Bank Table - Bank Name", false, emptyList()),
            FieldMappingV2("bankAccountsTable.branchAddress", "Bank Table - Branch Address", false, emptyList()),
            FieldMappingV2("bankAccountsTable.bicOrSwift", "Bank Table - BIC/SWIFT", false, emptyList()),
            FieldMappingV2("bankAccountsTable.iban", "Bank Table - IBAN", false, emptyList()),
            FieldMappingV2("bankAccountsTable.allocation", "Bank Table - Allocation", false, listOf(
                FieldMappingV2("percent", "Percent", false, emptyList()),
                FieldMappingV2("amount", "Amount", false, emptyList()),
                FieldMappingV2("remaining", "Remaining", false, emptyList())
            )),
            FieldMappingV2("bankAccountsTable.amount", "Bank Table - Amount", false, emptyList()),
            FieldMappingV2("bankAccountsTable.useForBonus", "Bank Table - Use For Bonus", false, emptyList()),
            FieldMappingV2("bankAccountsTable.id", "Bank Table - ID", false, emptyList()),
            FieldMappingV2("bankAccountsTable.changedBy", "Bank Table - Changed By", false, emptyList())
        )
    }

    fun getIntegrationFieldsMappingProfile(
        entityId: Long,
        integrationId: Long,
    ): IntegrationFieldsMappingOutputV2 {
        val integration = findIntegrationById(integrationId)
        val companyId = integration.companyId

        log.info("Fetching legal entity and company integration")
        val matchedLegalEntity = findLegalEntityById(companyId, entityId)

        return try {
            log.info("Attempting to fetch field mapping profile from field-mapping-service")
            buildFieldMappingOutput(companyId, entityId, integrationId, matchedLegalEntity, integration)
        } catch (e: Exception) {
            log.error("Error fetching field mapping profile from field-mapping-service: ${e.message}", e)
            IntegrationFieldsMappingOutputV2.newBuilder().build()
        }
    }

    private fun findLegalEntityById(companyId: Long, entityId: Long): CompanyOuterClass.LegalEntity {
        return newCompanyServiceAdapter.getLegalEntities(companyId)
            .find { it.id == entityId }
            ?: throw EntityNotFoundException("Not found legal entity with entityId=$entityId")
    }

    private fun buildFieldMappingOutput(
        companyId: Long,
        entityId: Long,
        integrationId: Long,
        matchedLegalEntity: CompanyOuterClass.LegalEntity,
        integration: JpaCompanyIntegration
    ): IntegrationFieldsMappingOutputV2 {
        val profile = getOrCreateProfile(companyId, entityId, integrationId, matchedLegalEntity, integration)
        val mappingStatus = determineMappingStatus(profile)
        val sourceFields = getKnitFieldsV2(integration)
        val targetFields = buildTargetFields(getOnboardDataSpecs(matchedLegalEntity, companyId))

        return dataMapper.map(
            profileId = UUID.fromString(profile.id),
            integrationId = integrationId,
            matchedLegalEntity = matchedLegalEntity,
            mappingStatus = mappingStatus.name,
            sourceFields = sourceFields,
            targetFields = targetFields,
            companyId = companyId
        )
    }

    private fun buildTargetFields(dataSpecs: List<BulkOnboardDataSpec>): List<FieldMappingV2> {
        return dataSpecs.map { dataSpec ->
            val children = dataSpec.valuesList.map { value ->
                FieldMappingV2.newBuilder()
                    .key(value)
                    .label(value)
                    .build()
            }

            FieldMappingV2.newBuilder()
                .key(dataSpec.key)
                .label(dataSpec.label)
                .isRequired(dataSpec.required)
                .children(children)
                .build()
        }
    }

    fun getIntegrationFieldsMappingContractorProfile(
        integrationId: Long,
    ): IntegrationFieldsMappingContractorOutput {
        val integration = findIntegrationById(integrationId)
        val companyId = integration.companyId

        return try {
            log.info("Attempting to fetch field mapping profile from field-mapping-service")
            buildContractorFieldMappingOutput(companyId, integrationId, integration)
        } catch (e: Exception) {
            log.error("Error fetching field mapping profile from field-mapping-service: ${e.message}", e)
            IntegrationFieldsMappingContractorOutput.newBuilder().build()
        }
    }

    private fun buildContractorFieldMappingOutput(
        companyId: Long,
        integrationId: Long,
        integration: JpaCompanyIntegration
    ): IntegrationFieldsMappingContractorOutput {
        val profile = getOrCreateProfile(companyId, null, integrationId, null, integration)
        val mappingStatus = determineMappingStatus(profile)
        val sourceFields = getKnitFieldsV2(integration)
        val targetFields = buildTargetFields(getOnboardDataSpecs(null, companyId))

        return dataMapper.map(
            profileId = UUID.fromString(profile.id),
            integrationId = integrationId,
            mappingStatus = mappingStatus.name,
            sourceFields = sourceFields,
            targetFields = targetFields,
            companyId = companyId
        )
    }

    /**
     * Gets an existing profile or creates a new one if none exists
     */
    private fun getOrCreateProfile(
        companyId: Long,
        entityId: Long?,
        integrationId: Long,
        legalEntity: CompanyOuterClass.LegalEntity?,
        integration: JpaCompanyIntegration
    ): Profile {
        val profiles = fieldMappingServiceAdapter.listProfiles(companyId)

        val profile = if (entityId == null && legalEntity == null) {
            findContractorProfile(profiles, integrationId, companyId, entityId, legalEntity, integration)
        } else {
            findEntityProfile(profiles, entityId, integrationId, companyId, legalEntity, integration)
        }

        // Update legal entity mapping status if applicable
        entityId?.let { updateLegalEntityMappingStatus(integrationId, it, profile) }

        return profile
    }

    private fun findContractorProfile(
        profiles: com.multiplier.fieldmapping.grpc.schema.ListProfilesResponse,
        integrationId: Long,
        companyId: Long,
        entityId: Long?,
        legalEntity: CompanyOuterClass.LegalEntity?,
        integration: JpaCompanyIntegration
    ): Profile {
        log.info("Fetching profile for contractor field mapping for companyId=$companyId")
        return profiles.profilesList.firstOrNull { profile ->
            isContractorProfile(profile, integrationId)
        } ?: createNewProfile(companyId, entityId, integrationId, legalEntity, integration)
    }

    private fun findEntityProfile(
        profiles: com.multiplier.fieldmapping.grpc.schema.ListProfilesResponse,
        entityId: Long?,
        integrationId: Long,
        companyId: Long,
        legalEntity: CompanyOuterClass.LegalEntity?,
        integration: JpaCompanyIntegration
    ): Profile {
        return profiles.profilesList.firstOrNull { profile ->
            isEntityProfile(profile, entityId, integrationId)
        } ?: createNewProfile(companyId, entityId, integrationId, legalEntity, integration)
    }

    private fun isContractorProfile(profile: Profile, integrationId: Long): Boolean {
        return profile.configMap.fieldsMap["ContractType"]?.stringValue == Contract.ContractType.CONTRACT_TYPE_CONTRACTOR.toString() &&
                profile.configMap.fieldsMap["integrationId"]?.stringValue == integrationId.toString()
    }

    private fun isEntityProfile(profile: Profile, entityId: Long?, integrationId: Long): Boolean {
        return profile.configMap.fieldsMap["entityId"]?.stringValue == entityId.toString() &&
                profile.configMap.fieldsMap["integrationId"]?.stringValue == integrationId.toString()
    }

    private fun updateLegalEntityMappingStatus(integrationId: Long, entityId: Long, profile: Profile) {
        val legalEntityMapping = legalEntityMappingRepository.findByIntegrationIdAndEntityId(integrationId, entityId)
            .orElseThrow { EntityNotFoundException("Not found legal entity mapping for entityId $entityId") }

        val status = determineMappingStatus(profile)
        log.info("Legal entity mapping status needs to be updated from ${legalEntityMapping.status} to $status")

        if (legalEntityMapping.status != status) {
            legalEntityMapping.status = status
            legalEntityMappingRepository.save(legalEntityMapping)
        }
    }

    /**
     * Creates a new profile for the entity and integration
     */
    private fun createNewProfile(
        companyId: Long,
        entityId: Long?,
        integrationId: Long,
        legalEntity: CompanyOuterClass.LegalEntity?,
        integration: JpaCompanyIntegration
    ): Profile {
        log.info("No field mapping profile found for entityId=$entityId and integrationId=$integrationId, creating new profile")

        if(entityId == null && legalEntity == null){
            log.info("Creating new profile for contractor field mapping for companyId=$companyId")
            val configMap = Struct.newBuilder().putAllFields(
                mapOf(
                    "ContractType" to Value.newBuilder().setStringValue(Contract.ContractType.CONTRACT_TYPE_CONTRACTOR.toString()).build(),
                    "integrationId" to Value.newBuilder().setStringValue(integrationId.toString()).build(),
                )
            ).build()

            val profileRequest = Profile.newBuilder()
                .setName("Contractor - ${integration.platform.name}")
                .setDescription("Profile for Contractor - ${integration.platform.name}")
                .setCompanyId(companyId)
                .setIsActive(true)
                .setConfigMap(configMap).addAllRules(emptyList()).build()

            return fieldMappingServiceAdapter.createProfile(profileRequest).profile
        }

        val configMap = Struct.newBuilder().putAllFields(
            mapOf(
                "entityId" to Value.newBuilder().setStringValue(entityId.toString()).build(),
                "integrationId" to Value.newBuilder().setStringValue(integrationId.toString()).build(),
            )
        ).build()

        val profileRequest = Profile.newBuilder()
            .setName("${legalEntity?.legalName} - ${integration.platform.name}")
            .setDescription("Profile for ${legalEntity?.legalName} - ${integration.platform.name}")
            .setCompanyId(companyId)
            .setIsActive(true)
            .setConfigMap(configMap).addAllRules(emptyList()).build()

        return fieldMappingServiceAdapter.createProfile(profileRequest).profile
    }

    /**
     * Determines the mapping status based on the profile and its rules
     */
    private fun determineMappingStatus(
        profile: Profile
    ): LegalMappingStatus {
        if (!profile.isActive || profile.rulesList.isEmpty()) {
            return LegalMappingStatus.UNMAPPED
        }

        // Check if any required fields are unmapped
        val hasUnmappedRequiredFields = profile.rulesList.any { rule ->
            rule.isRequired && rule.sourceField.isNullOrEmpty()
        }

        return if (hasUnmappedRequiredFields) {
            LegalMappingStatus.PARTIALLY_MAPPED
        } else {
            LegalMappingStatus.FULLY_MAPPED
        }
    }
}